import 'package:json_annotation/json_annotation.dart';
import 'package:vp_trading/model/enum/derivative/derivative_order_status_enum.dart';
import 'package:vp_trading/model/enum/take_profit_trigger_condition_enum.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_item_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';

part 'condition_order_book_model.g.dart';

@JsonSerializable()
class ConditionOrderBookModel {
  /// Số tiểu khoản
  final String? accountId;

  /// Gi<PERSON> kích hoạt
  final num? activePrice;

  /// <PERSON><PERSON><PERSON> kích hoạt cắt lỗ
  final num? activepriceSL;

  /// <PERSON><PERSON><PERSON> chốt lời lệnh TPSL
  final num? activepriceTP;

  /// Khối lượng kích hoạt
  final num? activeQty;

  /// Thời gian kích hoạt, format: dd/mm/yyyy hh:mm:ss
  final String? activeTime;

  /// Loại điều kiện kích hoạt
  final String? activeType;

  /// Thông tin bổ sung (nếu có)
  final Map<String, dynamic>? additionalData;

  /// Cho phép Sửa (Y/N)
  final String? allowAmend;

  /// Cho phép Hủy (Y/N)
  final String? allowCancel;

  /// Khối lượng đã hủy
  final num? cancelQty;

  /// Người đặt (Mã môi giới/Mã KH)
  final String? createBy;

  /// Thời gian đặt lệnh
  final String? createTime;

  /// Loại biên độ
  final String? deltaType;

  /// Biên độ
  final num? deltaValue;

  /// Nội dung lỗi (tiếng Anh)
  final String? enErrorDesc;

  /// Chi tiết lỗi (tiếng Anh)
  final String? enErrorDescDtl;

  /// Nội dung lỗi
  final String? errorDesc;

  /// Chi tiết lỗi
  final String? errorDescDtl;

  /// Giá trị khớp của lệnh
  final num? execAmt;

  /// Giá khớp trung bình của lệnh
  final num? execPrice;

  /// Khối lượng đã khớp của lệnh
  final num? execQty;

  /// Phí
  final String? feeAmt;

  /// Message từ Sở
  final String? feedbackMsg;

  /// Tỷ lệ phí
  final String? feeRate;

  /// Ngày bắt đầu hiệu lực
  final String? fromDate;

  /// Giá kích hoạt ban đầu
  final num? fx1Price;

  /// Thời gian thay đổi gần nhất, format: dd/mm/yyyy hh:mm:ss
  final String? lastChange;

  /// Giá đặt lệnh vị thế mở LO - TPSL
  final num? openPrice;

  /// Số hiệu lệnh
  final String? orderId;

  /// Mã trạng thái lệnh
  final String? orderStatus;

  /// Loại lệnh điều kiện (BB/TSO/STO/TPSL)
  final String? orderType;

  /// Số hiệu lệnh gốc (với lệnh sửa/hủy)
  final String? originOrderId;

  /// Giá đặt
  final String? price;

  /// Giá đặt cắt lỗ
  final String? priceSL;

  /// Bước giá
  final String? priceStep;

  /// Giá đặt chốt lời
  final String? priceTP;

  /// Loại lệnh (LO/ATO/ATC/MTL...)
  final String? priceType;

  /// Loại giá cắt lỗ với lệnh TPSL
  final String? priceTypeSL;

  /// Loại giá chốt lời với lệnh TPSL
  final String? priceTypeTP;

  /// Khối lượng
  final num? qty;

  /// Khối lượng còn lại chờ khớp
  final num? remainQty;

  /// Nguồn lệnh
  final String? serviceName;

  /// Chiều mua/bán
  final String? side;

  /// Đánh dấu chia/không chia lệnh (Y/N)
  final String? split;

  /// Giá kích hoạt SL lệnh TPSL
  final num? stopLosses;

  /// Giá đặt lệnh SL lệnh TPSL
  final num? stopLosses2;

  /// Giá dừng
  final String? stopPrice;

  /// Giá chốt lời TPSL
  final num? stopPrice2;

  /// Mã CK/HĐTL/TP...
  final String? symbol;

  /// Thuế
  final String? taxAmt;

  /// Loại lệnh (T: Today, G: Good till cancel)
  final String? timeTypeValue;

  /// Ngày kết thúc hiệu lực
  final String? toDate;

  /// Ngày giao dịch
  final String? tradeDate;

  /// Ngày giờ giao dịch
  final String? tradeTime;

  /// Số hiệu giao dịch hệ thống
  final String? transId;

  /// Hành động khi xảy ra sự kiện quyền pha loãng giá cổ phiếu
  final String? diluationAction;

  // Biên trượt
  final num? slipPagePrice;

  final String? via;

  //Giá vốn
  final num? costPrice;

  /// Loại điều kiện kích hoạt (Tỷ lệ chốt lời, Biên giá chốt lời, etc.)
  final String? triggerCondition;

  const ConditionOrderBookModel({
    this.accountId,
    this.activePrice,
    this.activepriceSL,
    this.activepriceTP,
    this.activeQty,
    this.activeTime,
    this.activeType,
    this.additionalData,
    this.allowAmend,
    this.allowCancel,
    this.cancelQty,
    this.createBy,
    this.createTime,
    this.deltaType,
    this.deltaValue,
    this.enErrorDesc,
    this.enErrorDescDtl,
    this.errorDesc,
    this.errorDescDtl,
    this.execAmt,
    this.execPrice,
    this.execQty,
    this.feeAmt,
    this.feedbackMsg,
    this.feeRate,
    this.fromDate,
    this.fx1Price,
    this.lastChange,
    this.openPrice,
    this.orderId,
    this.orderStatus,
    this.orderType,
    this.originOrderId,
    this.price,
    this.priceSL,
    this.priceStep,
    this.priceTP,
    this.priceType,
    this.priceTypeSL,
    this.priceTypeTP,
    this.qty,
    this.remainQty,
    this.serviceName,
    this.side,
    this.split,
    this.stopLosses,
    this.stopLosses2,
    this.stopPrice,
    this.stopPrice2,
    this.symbol,
    this.taxAmt,
    this.timeTypeValue,
    this.toDate,
    this.tradeDate,
    this.tradeTime,
    this.transId,
    this.diluationAction,
    this.slipPagePrice,
    this.via,
    this.costPrice,
    this.triggerCondition,
  });

  factory ConditionOrderBookModel.fromJson(Map<String, dynamic> json) =>
      _$ConditionOrderBookModelFromJson(json);

  Map<String, dynamic> toJson() => _$ConditionOrderBookModelToJson(this);
}

extension ConditionOrderBookModelExtension on ConditionOrderBookModel {
  ConditionOrderViewStatusEnum get orderViewStatusEnum =>
      ConditionOrderViewStatusEnumExt.conditionOrderStatusFromString(
        orderStatus,
      );

  ConditionOrderStatusEnum get orderStatusEnum =>
      ConditionOrderStatusEnumExt.conditionOrderStatusFromString(orderStatus);

  DerivativeOrderStatusEnum get derivativeOrderStatusEnum =>
      DerivativeOrderStatusEnumExt.orderStatusFromString(orderStatus ?? '');

  OrderTypeEnum get orderTypeEnum => OrderTypeEnumExt.orderTypeFromString(side);

  OrderTypeEnum get orderTypeFUEnum =>
      OrderTypeEnumExt.orderTypeFromStringConditionFU(side);

  ConditionOrderTypeEnum get conditionOrderTypeEnum =>
      ConditionOrderTypeEnumExt.conditionOrderTypeFromString(orderType);

  ConditionOrderTypeFuEnum get conditionOrderTypeFuEnum =>
      ConditionOrderTypeFuEnumExt.conditionOrderTypeFuFromString(orderType);

  String get diluationActionFormat =>
      diluationAction == "CANCEL" ? "Hủy lệnh" : (diluationAction ?? "-");

  bool get isEnableEdit => allowAmend == "Y";

  bool get isEnableCancel => allowCancel == "Y";

  String get viaName {
    switch (via) {
      case 'V':
        return 'App NeoInvestPro';
      case 'D':
        return 'Web NeoInvestPro';
      case 'N':
        return 'NeoAPI';
      case 'F':
        return 'Counter';
      case 'A':
        return 'Tất cả';
      case 'O':
        return 'Web';
      case 'T':
        return 'Tele';
      case 'M':
        return 'Moblie';
      case 'H':
        return 'Home Trading';
      case 'Y':
        return 'Mobile mới';
      case 'Z':
        return 'Web app mới';
      case 'C':
        return 'Cash Service';
      case 'P':
        return 'Copy Trade';
      case 'R':
        return 'ePartner';
      case 'G':
        return 'Lệnh điều kiện';
      case 'W':
        return 'MM';
      case 'L':
        return 'Weath';
      default:
        return '-';
    }
  }

  /// Determine trigger condition type based on triggerCondition field
  TakeProfitTriggerConditionEnum get triggerConditionEnum {
    final condition = triggerCondition;

    if (condition != null) {
      // Check if it contains rate/percentage keywords
      if (condition.contains('Tỷ lệ') ||
          condition.contains('tỷ lệ') ||
          condition.contains('%')) {
        return TakeProfitTriggerConditionEnum.rateProfit;
      }
      // Check if it contains price/margin keywords
      else if (condition.contains('Biên giá') ||
          condition.contains('biên giá') ||
          condition.contains('giá')) {
        return TakeProfitTriggerConditionEnum.slippageProfit;
      }
    }

    // Default to rateProfit if no clear indication
    return TakeProfitTriggerConditionEnum.rateProfit;
  }
}
