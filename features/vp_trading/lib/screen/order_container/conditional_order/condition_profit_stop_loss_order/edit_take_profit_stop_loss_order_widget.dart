import 'package:flutter/material.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/order_edit/edit_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/enum/take_profit_trigger_condition_enum.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/order_container/conditional_order/edit_condition_order/edit_effective_time_widget.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';
import 'package:vp_trading/screen/place_order/take_profit_stop_loss/take_profit_stop_loss_volume_widget.dart';
import 'package:vp_trading/screen/place_order/take_profit_stop_loss/widget/slippage_text_input_field.dart';
import 'package:vp_trading/screen/place_order/take_profit_stop_loss/widget/take_profit_choice_trigger_condition.dart';
import 'package:vp_trading/screen/place_order/take_profit_stop_loss/widget/trigger_text_input_field.dart';
import 'package:vp_trading/screen/place_order/widgets/available_trade/condition_available_trade_label.dart';
import 'package:vp_trading/screen/place_order/widgets/choice_dilution_action_widget.dart';

class EditTakeProfitStopLossOrderWidget extends StatefulWidget {
  const EditTakeProfitStopLossOrderWidget({
    super.key,
    required this.item,
    required this.onEditSuccess,
  });

  final ConditionOrderBookModel item;
  final VoidCallback onEditSuccess;

  @override
  State<EditTakeProfitStopLossOrderWidget> createState() =>
      _EditTakeProfitStopLossOrderWidgetState();
}

class _EditTakeProfitStopLossOrderWidgetState
    extends State<EditTakeProfitStopLossOrderWidget> {
  final _triggerController = TextEditingController();
  final _slippageController = TextEditingController();
  final _volumeController = TextEditingController();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeState();
    });
  }

  /// Initialize basic order information (symbol, orderType, costPrice, etc.)
  void _initializeOrderInfo() {
    // Set order type in PlaceOrderCubit
    final orderType =
        widget.item.conditionOrderTypeEnum == ConditionOrderTypeEnum.tpo
            ? OrderType.takeProfit
            : OrderType.stopLoss;
    context.read<PlaceOrderCubit>().updateOrderType(orderType);

    // Initialize ValidateConditionOrderCubit with available data
    final validateConditionCubit = context.read<ValidateConditionOrderCubit>();

    // Set effective time if available
    if (widget.item.fromDate != null && widget.item.toDate != null) {
      final fromDate = _parseDate(widget.item.fromDate);
      final toDate = _parseDate(widget.item.toDate);
      if (fromDate != null && toDate != null) {
        validateConditionCubit.setEffectiveTime(fromDate, toDate);
      }
    }

    // Initialize costPrice if available
    if (widget.item.costPrice != null && widget.item.costPrice! > 0) {
      // Set costPrice in the cubit state
      validateConditionCubit.setCostPrice(widget.item.costPrice ?? 0);
    }
  }

  /// Get trigger condition type from model
  TakeProfitTriggerConditionEnum _determineTriggerCondition() {
    return widget.item.triggerConditionEnum;
  }

  void _initializeState() {
    // Initialize basic order information first
    _initializeOrderInfo();

    String? triggerValueStr;
    TakeProfitTriggerConditionEnum triggerCondition =
        _determineTriggerCondition();

    // Check if this is a rate-based trigger condition (Tỷ lệ cắt lỗ/chốt lời)
    if (triggerCondition == TakeProfitTriggerConditionEnum.rateProfit) {
      // For rate-based conditions, use stopLossRate field
      if (widget.item.stopLossRate != null && widget.item.stopLossRate! > 0) {
        triggerValueStr = widget.item.stopLossRate.toString();
      }
    } else {
      // For price-based conditions (Biên giá cắt lỗ/chốt lời), use existing logic
      // For take profit orders (TPO)
      if (widget.item.conditionOrderTypeEnum == ConditionOrderTypeEnum.tpo) {
        if (widget.item.activepriceTP != null &&
            widget.item.activepriceTP! > 0) {
          triggerValueStr = widget.item.activepriceTP.toString();
        } else if (widget.item.priceTP != null &&
            widget.item.priceTP!.isNotEmpty) {
          triggerValueStr = widget.item.priceTP!;
        }
      }
      // For stop loss orders (SLO)
      else if (widget.item.conditionOrderTypeEnum ==
          ConditionOrderTypeEnum.slo) {
        if (widget.item.activepriceSL != null &&
            widget.item.activepriceSL! > 0) {
          triggerValueStr = widget.item.activepriceSL.toString();
        } else if (widget.item.priceSL != null &&
            widget.item.priceSL!.isNotEmpty) {
          triggerValueStr = widget.item.priceSL!;
        }
      }

      // Fallback to general fields for price-based conditions
      if (triggerValueStr == null) {
        if (widget.item.activePrice != null && widget.item.activePrice! > 0) {
          triggerValueStr = widget.item.activePrice.toString();
        } else if (widget.item.price != null && widget.item.price!.isNotEmpty) {
          triggerValueStr = widget.item.price!;
        } else if (widget.item.stopPrice != null &&
            widget.item.stopPrice!.isNotEmpty) {
          triggerValueStr = widget.item.stopPrice!;
        }
      }
    }

    if (triggerValueStr != null) {
      final triggerValue = double.tryParse(triggerValueStr) ?? 0;

      String displayTrigger;
      if (triggerCondition == TakeProfitTriggerConditionEnum.rateProfit) {
        // For rate-based (%), display as-is (e.g., 3 for 3%)
        displayTrigger = triggerValue.toString();
      } else {
        // For price-based, divide by 1000 if needed
        if (triggerValue > 1000) {
          displayTrigger = (triggerValue / 1000).toString();
        } else {
          displayTrigger = triggerValue.toString();
        }
      }

      _triggerController.text = displayTrigger;

      // Set trigger condition type
      context.read<ValidateConditionOrderCubit>().setTriggerConditionEnum(
        triggerCondition,
      );

      context.read<ValidateConditionOrderCubit>().onChangeTrigger(
        displayTrigger,
      );

      // Order type is already set in _initializeOrderInfo()
    }

    // Pre-fill slippage value (always divide by 1000 for display)
    if (widget.item.slipPagePrice != null && widget.item.slipPagePrice! > 0) {
      final slippageValue = widget.item.slipPagePrice!.toDouble();

      // Always divide by 1000 for display
      final displaySlippage = (slippageValue / 1000).toString();

      _slippageController.text = displaySlippage;
      context.read<ValidateConditionOrderCubit>().onChangeSlippage(
        displaySlippage,
      );
    }

    // Pre-fill volume
    if (widget.item.qty != null) {
      final volumeStr = MoneyUtils.formatMoney(
        (widget.item.qty ?? 0).toDouble(),
        suffix: '',
      );
      _volumeController.text = volumeStr;
      context.read<ValidateOrderCubit>().onChangeVolumne(volumeStr);
    }
  }

  DateTime? _parseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      print('Parsing date: $dateString');
      final parts = dateString.split('/');
      if (parts.length == 3) {
        final result = DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
        print('Parsed result: $result');
        return result;
      }
    } catch (e) {
      print('Parse date error: $e');
    }
    print('Parse date failed, returning null');
    return null;
  }

  bool _hasFieldsChanged() {
    // Get original values based on trigger condition type
    double originalTriggerValue = 0.0;
    double originalSlippageValue = 0.0;
    int originalVolume = 0;

    final triggerCondition = _determineTriggerCondition();

    // Check if this is a rate-based trigger condition (Tỷ lệ cắt lỗ/chốt lời)
    if (triggerCondition == TakeProfitTriggerConditionEnum.rateProfit) {
      // For rate-based conditions, use stopLossRate field
      if (widget.item.stopLossRate != null && widget.item.stopLossRate! > 0) {
        originalTriggerValue = widget.item.stopLossRate!.toDouble();
      }
    } else {
      // For price-based conditions (Biên giá cắt lỗ/chốt lời), use existing logic
      // For take profit orders (TPO)
      if (widget.item.conditionOrderTypeEnum == ConditionOrderTypeEnum.tpo) {
        if (widget.item.activepriceTP != null &&
            widget.item.activepriceTP! > 0) {
          originalTriggerValue = widget.item.activepriceTP!.toDouble();
        } else if (widget.item.priceTP != null &&
            widget.item.priceTP!.isNotEmpty) {
          originalTriggerValue = double.tryParse(widget.item.priceTP!) ?? 0.0;
        }
      }
      // For stop loss orders (SLO)
      else if (widget.item.conditionOrderTypeEnum ==
          ConditionOrderTypeEnum.slo) {
        if (widget.item.activepriceSL != null &&
            widget.item.activepriceSL! > 0) {
          originalTriggerValue = widget.item.activepriceSL!.toDouble();
        } else if (widget.item.priceSL != null &&
            widget.item.priceSL!.isNotEmpty) {
          originalTriggerValue = double.tryParse(widget.item.priceSL!) ?? 0.0;
        }
      }

      // Fallback to general fields for price-based conditions
      if (originalTriggerValue == 0.0) {
        if (widget.item.activePrice != null && widget.item.activePrice! > 0) {
          originalTriggerValue = widget.item.activePrice!.toDouble();
        } else if (widget.item.price != null && widget.item.price!.isNotEmpty) {
          originalTriggerValue = double.tryParse(widget.item.price!) ?? 0.0;
        } else if (widget.item.stopPrice != null &&
            widget.item.stopPrice!.isNotEmpty) {
          originalTriggerValue = double.tryParse(widget.item.stopPrice!) ?? 0.0;
        }
      }
    }

    originalSlippageValue = (widget.item.slipPagePrice ?? 0.0).toDouble();
    originalVolume = widget.item.qty?.toInt() ?? 0;

    // Get current values
    final validateConditionState =
        context.read<ValidateConditionOrderCubit>().state;

    // For trigger value comparison, handle rate vs price differently
    double currentTriggerValue;
    if (triggerCondition == TakeProfitTriggerConditionEnum.rateProfit) {
      // For rate-based (%), use direct value
      currentTriggerValue = double.tryParse(_triggerController.text) ?? 0.0;
    } else {
      // For price-based, use price extension to convert to server format
      currentTriggerValue = _triggerController.text.price ?? 0.0;
    }

    // For slippage comparison, convert display value back to server format (multiply by 1000)
    final currentSlippageDisplayValue =
        double.tryParse(_slippageController.text) ?? 0.0;
    final currentSlippageValue = currentSlippageDisplayValue * 1000;
    final currentVolume = _volumeController.text.volume.toInt();

    // Compare effective time
    final originalFromDate = widget.item.fromDate ?? '';
    final originalToDate = widget.item.toDate ?? '';
    final currentFromDate = validateConditionState.fromDate ?? '';
    final currentToDate = validateConditionState.toDate ?? '';

    bool datesChanged = false;

    if (originalFromDate.isNotEmpty) {
      datesChanged = originalFromDate != currentFromDate;
    }

    if (!datesChanged && originalToDate.isNotEmpty) {
      datesChanged = originalToDate != currentToDate;
    }

    final triggerChanged = originalTriggerValue != currentTriggerValue;
    final slippageChanged = originalSlippageValue != currentSlippageValue;
    final volumeChanged = originalVolume != currentVolume;

    final hasChanges =
        triggerChanged || slippageChanged || volumeChanged || datesChanged;

    return hasChanges;
  }

  void _handleSave() {
    final validateConditionState =
        context.watch<ValidateConditionOrderCubit>().state;

    final triggerValue = double.tryParse(_triggerController.text) ?? 0;
    final slippageDisplayValue =
        double.tryParse(_slippageController.text) ?? 0.0;
    final slippageValue =
        slippageDisplayValue * 1000; // Multiply by 1000 for server
    final qty = _volumeController.text.volume.toInt();
    final serverTriggerValue = (_triggerController.text.price ?? 0.0).round();

    // Calculate the actual order price based on trigger condition and order type
    final orderPrice = validateConditionState.orderPriceAbs;

    // Debug effective time values
    print('=== DEBUG EFFECTIVE TIME ===');
    print('Original fromDate: ${widget.item.fromDate}');
    print('Original toDate: ${widget.item.toDate}');
    print(
      'ValidateConditionState fromDate: ${validateConditionState.fromDate}',
    );
    print('ValidateConditionState toDate: ${validateConditionState.toDate}');

    // Always prioritize original values from model to avoid EditEffectiveTimeWidget override
    final finalFromDate =
        widget.item.fromDate ?? validateConditionState.fromDate ?? '';
    final finalToDate =
        widget.item.toDate ?? validateConditionState.toDate ?? '';
    print('Final fromDate: $finalFromDate');
    print('Final toDate: $finalToDate');
    print('=== END DEBUG ===');

    // Call update API
    context.read<EditConditionOrderCubit>().updateConditionOrder(
      request: ConditionOrderRequestModel(
        requestId: "app_${AppHelper().genXRequestID()}",
        orderType: widget.item.orderType ?? '',
        accountId: widget.item.accountId ?? '',
        orderId: widget.item.orderId ?? '',
        conditionInfo: ConditionInfo(
          symbol: widget.item.symbol ?? '',
          qty: qty,
          side: widget.item.side?.toLowerCase() ?? '',
          type: "limit",
          price: orderPrice,
          fromDate: finalFromDate,
          toDate: finalToDate,
          slipPagePrice: slippageValue,
          stopLossRate:
              validateConditionState.triggerConditionEnum ==
                      TakeProfitTriggerConditionEnum.rateProfit
                  ? triggerValue
                  : null,
          stopLossPriceAmp:
              validateConditionState.triggerConditionEnum ==
                      TakeProfitTriggerConditionEnum.slippageProfit
                  ? serverTriggerValue
                  : null,
          activeType:
              widget.item.conditionOrderTypeEnum == ConditionOrderTypeEnum.tpo
                  ? ActivationConditionsType.greaterThan.toParamRequest()
                  : ActivationConditionsType.lessThan.toParamRequest(),
          costPrice: validateConditionState.costPrice,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _triggerController.dispose();
    _slippageController.dispose();
    _volumeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<EditConditionOrderCubit, EditConditionOrderState>(
      listener: (context, state) {
        if (state.status == EditConditionOrderStatus.success) {
          widget.onEditSuccess();
        } else if (state.status == EditConditionOrderStatus.failure) {
          // Show error message
          context.showSnackBar(
            content: state.errorMessage ?? "-",
            snackBarType: VPSnackBarType.error,
          );
        }
      },
      child: Column(
        children: [
          // Available Trade Label (only for sell orders)
          BlocSelector<PlaceOrderCubit, PlaceOrderState, OrderAction?>(
            selector: (state) => state.action,
            builder: (_, action) {
              if (action == OrderAction.buy) {
                return const SizedBox.shrink();
              }
              return const ConditionAvailableTradeLabel();
            },
          ),

          const SizedBox(height: 8),

          // Trigger Component
          _buildTriggerComponent(),

          const SizedBox(height: 8),

          // Slippage Input
          SlippageTextInputField(slippageController: _slippageController),

          const SizedBox(height: 8),

          // Order Price Display
          _buildOrderPriceDisplay(),

          // Volume Input
          TakeProfitStopLossVolumeWidget(volumeController: _volumeController),

          const SizedBox(height: 8),

          // Effective Time
          EditEffectiveTimeWidget(
            initialFromDate: _parseDate(widget.item.fromDate),
            initialToDate: _parseDate(widget.item.toDate),
          ),

          const SizedBox(height: 8),

          // Dilution Action
          const ChoiceDilutionActionWidget(),

          const SizedBox(height: 24),

          // Action Buttons
          BlocBuilder<EditConditionOrderCubit, EditConditionOrderState>(
            builder: (context, editState) {
              return BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
                builder: (context, validateOrderState) {
                  return BlocBuilder<
                    ValidateConditionOrderCubit,
                    ValidateConditionOrderState
                  >(
                    builder: (context, validateConditionState) {
                      final hasChanges = _hasFieldsChanged();

                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: VpsButton.primaryXsSmall(
                          title: "Sửa lệnh",
                          onPressed:
                              editState.status ==
                                          EditConditionOrderStatus.loading ||
                                      !hasChanges
                                  ? null
                                  : _handleSave,
                          alignment: Alignment.center,
                          disabled:
                              editState.status ==
                                  EditConditionOrderStatus.loading ||
                              !hasChanges ||
                              (validateConditionState.errorTrigger.isError ||
                                  validateConditionState
                                      .errorSlippage
                                      .isError ||
                                  validateOrderState.errorVolume.isError),
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTriggerComponent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Điều kiện kích hoạt",
            style: context.textStyle.subtitle14?.copyWith(
              color: vpColor.textPrimary,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: BlocListener<
                  ValidateConditionOrderCubit,
                  ValidateConditionOrderState
                >(
                  listener: (context, state) {
                    // Listener for state changes
                  },
                  child: const TakeProfitChoiceTriggerCondition(),
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: TriggerTextInputField(
                  triggerController: _triggerController,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderPriceDisplay() {
    return BlocBuilder<
      ValidateConditionOrderCubit,
      ValidateConditionOrderState
    >(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                VPTradingLocalize.current.trading_order_price,
                style: context.textStyle.body14?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
              Text(
                state.orderPriceDisplay,
                style: context.textStyle.subtitle14?.copyWith(
                  color: vpColor.textPriceGreen,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
